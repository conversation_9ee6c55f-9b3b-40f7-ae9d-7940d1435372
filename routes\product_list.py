from flask import Blueprint, jsonify, request, send_file
from models import db, Store, UnifiedListProducts
from translate.deeplx_api import DeepLXTranslator
from translate.deepseekhuoshan_api import DeepSeekhuoshan
from translate.mtranserver_api import MTranServer
from translate.deepseekmtserver_api import DeepSeekMTranServer
import pandas as pd
import os
from datetime import datetime
import tempfile
from werkzeug.utils import secure_filename
import openpyxl
from openpyxl.styles import Font, PatternFill
import io
from sqlalchemy import text, or_
from openpyxl.worksheet.datavalidation import DataValidation
import logging
import asyncio
from translate.translator_factory import TranslatorFactory
import aiohttp
from models import db, UnifiedListProducts, Store, SystemSettings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义产品表头常量
PRODUCT_HEADERS = {
    'operation_type': '操作类型*',
    'category_id': '分类ID*',
    'sku': 'SKU*',
    'ean': 'EAN',
    'title_en': '英文标题*',
    'title_cn': '中文标题',
    'title_lt': '立陶宛语标题',
    'title_lv': '拉脱维亚语标题',
    'title_et': '爱沙尼亚语标题',
    'title_fi': '芬兰语标题',
    'description_en': '英文描述*',
    'description_lt': '立陶宛语描述',
    'description_lv': '拉脱维亚语描述',
    'description_et': '爱沙尼亚语描述',
    'description_fi': '芬兰语描述',
    'image_url1': '图片链接1*',
    'image_url2': '图片链接2',
    'image_url3': '图片链接3',
    'image_url4': '图片链接4',
    'image_url5': '图片链接5',
    'package_length': '包装长度(m)*',
    'package_width': '包装宽度(m)*',
    'package_height': '包装高度(m)*',
    'package_weight': '包装重量(kg)*',
    'notes': '备注'
}

# 反向映射，用于导入时的字段匹配
PRODUCT_HEADERS_REVERSE = {v: k for k, v in PRODUCT_HEADERS.items()}

product_bp = Blueprint('product', __name__)

@product_bp.route('/api/product/delete/<int:product_id>', methods=['DELETE'])
def delete_product(product_id):
    try:
        # 直接从统一表中查询和删除
        product = UnifiedListProducts.query.get_or_404(product_id)
        db.session.delete(product)
        db.session.commit()
        return jsonify({'message': '产品删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@product_bp.route('/api/product/batch-delete', methods=['POST'])
def batch_delete_products():
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        
        if not product_ids:
            return jsonify({'error': '没有选择要删除的产品'}), 400
            
        # 批量删除
        result = UnifiedListProducts.query.filter(
            UnifiedListProducts.id.in_(product_ids)
        ).delete(synchronize_session=False)
        
        db.session.commit()
        return jsonify({
            'message': f'成功删除 {result} 个产品'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/product/edit/<int:product_id>', methods=['GET', 'PUT'])
def edit_product(product_id):
    try:
        # 直接从统一表中查询产品
        product = UnifiedListProducts.query.get_or_404(product_id)
        
        if request.method == 'GET':
            # 获取产品信息
            return jsonify(product.to_dict())
        else:  # PUT
            # 更新产品信息
            data = request.get_json()
            
            # 更新字段
            for key, value in data.items():
                if hasattr(product, key):
                    setattr(product, key, value)
            
            db.session.commit()
            return jsonify({'message': '产品更新成功'})
            
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@product_bp.route('/api/list-products')
def get_list_products():
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '')
        seller_id = request.args.get('seller_id')
        
        # 构建基础查询
        query = UnifiedListProducts.query
        
        # 应用筛选条件
        if seller_id:
            query = query.filter(UnifiedListProducts.seller_id == seller_id)
        if search:
            query = query.filter(
                or_(
                    UnifiedListProducts.title_en.ilike(f'%{search}%'),
                    UnifiedListProducts.title_cn.ilike(f'%{search}%'),
                    UnifiedListProducts.sku.ilike(f'%{search}%'),
                    UnifiedListProducts.ean.ilike(f'%{search}%')
                )
            )
        if status:
            query = query.filter(UnifiedListProducts.status == status)
            
        # 获取分页数据
        pagination = query.paginate(page=page, per_page=per_page)
        
        return jsonify({
            'products': [product.to_dict() for product in pagination.items],
            'pagination': {
                'current_page': page,
                'per_page': per_page,
                'total_pages': pagination.pages,
                'total_count': pagination.total
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    


# Excel模板下载API
@product_bp.route('/api/product/template/download', methods=['GET'])
def download_template():
    """下载Excel导入模板"""
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "产品导入模板"

        # 使用常量中定义的表头
        headers = list(PRODUCT_HEADERS.values())

        # 设置表头和样式
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")

        # 为操作类型添加数据验证
        dv = DataValidation(type="list", formula1='"update,delete"', allow_blank=True)
        ws.add_data_validation(dv)
        dv.add(f'A2:A1048576')  # 应用到整个A列（除表头外）

        # 设置列宽
        for col in ws.columns:
            ws.column_dimensions[col[0].column_letter].width = 14

        # 保存到内存中
        excel_file = io.BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)

        return send_file(
            excel_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'自建产品导入模板.xlsx'
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Excel导入API
@product_bp.route('/api/product/import', methods=['POST'])
def import_products():
    """导入产品数据"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if not file.filename:
            return jsonify({'error': '没有选择文件'}), 400

        # 验证文件类型
        allowed_extensions = {'xlsx', 'xls', 'csv'}
        if not file.filename.lower().endswith(tuple('.' + ext for ext in allowed_extensions)):
            return jsonify({'error': '不支持的文件格式'}), 400

        # 获取参数
        store_id = request.form.get('store_id')
        update_existing = request.form.get('update_existing', 'false').lower() == 'true'
        skip_errors = request.form.get('skip_errors', 'false').lower() == 'true'

        if not store_id:
            return jsonify({'error': '请选择店铺'}), 400

        # 保存文件到临时目录
        temp_dir = tempfile.mkdtemp()
        temp_path = os.path.join(temp_dir, secure_filename(file.filename))
        file.save(temp_path)

        # 读取Excel文件
        try:
            if file.filename.endswith('.csv'):
                df = pd.read_csv(temp_path, na_filter=True)
            else:
                df = pd.read_excel(temp_path, na_filter=True)
            
            # 处理DataFrame中的nan值
            df = df.fillna('')  # 将所有NaN值替换为空字符串
            
        except Exception as e:
            os.remove(temp_path)
            os.rmdir(temp_dir)
            return jsonify({'error': f'文件读取失败: {str(e)}'}), 400

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # 获取操作类型
                operation_type = str(row[PRODUCT_HEADERS['operation_type']]).lower().strip()
                if operation_type not in ['update', 'delete']:
                    raise Exception(f'无效的操作类型: {operation_type}，必须是 update 或 delete')

                # 检查是否存在相同SKU的产品
                existing_product = UnifiedListProducts.query.filter_by(
                    seller_id=store_id, 
                    sku=str(row[PRODUCT_HEADERS['sku']])
                ).first()

                if operation_type == 'delete':
                    if existing_product:
                        db.session.delete(existing_product)
                        db.session.commit()
                        success_count += 1
                    else:
                        raise Exception(f'要删除的SKU不存在: {row[PRODUCT_HEADERS["sku"]]}')
                else:  # update
                    # 构建产品数据
                    product_data = {
                        'seller_id': store_id,
                        'sku': str(row[PRODUCT_HEADERS['sku']]),
                        'ean': str(row.get(PRODUCT_HEADERS['ean'], '')),
                        'category_id': int(row[PRODUCT_HEADERS['category_id']]),
                        'title_en': str(row[PRODUCT_HEADERS['title_en']]),
                        'title_cn': str(row.get(PRODUCT_HEADERS['title_cn'], '')),
                        'title_lt': str(row.get(PRODUCT_HEADERS['title_lt'], '')),
                        'title_lv': str(row.get(PRODUCT_HEADERS['title_lv'], '')),
                        'title_et': str(row.get(PRODUCT_HEADERS['title_et'], '')),
                        'title_fi': str(row.get(PRODUCT_HEADERS['title_fi'], '')),
                        'description_en': str(row[PRODUCT_HEADERS['description_en']]),
                        'description_lt': str(row.get(PRODUCT_HEADERS['description_lt'], '')),
                        'description_lv': str(row.get(PRODUCT_HEADERS['description_lv'], '')),
                        'description_et': str(row.get(PRODUCT_HEADERS['description_et'], '')),
                        'description_fi': str(row.get(PRODUCT_HEADERS['description_fi'], '')),
                        'image_url1': str(row[PRODUCT_HEADERS['image_url1']]),
                        'image_url2': str(row.get(PRODUCT_HEADERS['image_url2'], '')),
                        'image_url3': str(row.get(PRODUCT_HEADERS['image_url3'], '')),
                        'image_url4': str(row.get(PRODUCT_HEADERS['image_url4'], '')),
                        'image_url5': str(row.get(PRODUCT_HEADERS['image_url5'], '')),
                        'package_length': float(row[PRODUCT_HEADERS['package_length']]),
                        'package_width': float(row[PRODUCT_HEADERS['package_width']]),
                        'package_height': float(row[PRODUCT_HEADERS['package_height']]),
                        'package_weight': float(row[PRODUCT_HEADERS['package_weight']]),
                        'notes': str(row.get(PRODUCT_HEADERS['notes'], ''))
                    }

                    # 处理所有字段的nan值和空值
                    for key in product_data:
                        if isinstance(product_data[key], str):
                            if product_data[key].lower() == 'nan' or not product_data[key].strip():
                                product_data[key] = ''

                    if existing_product:
                        # 更新现有产品
                        for key, value in product_data.items():
                            setattr(existing_product, key, value)
                    else:
                        # 创建新产品
                        new_product = UnifiedListProducts(**product_data)
                        db.session.add(new_product)
                    
                    db.session.commit()
                    success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f'第{index + 2}行: {str(e)}')
                if not skip_errors:
                    return jsonify({
                        'error': f'导入失败: {str(e)}',
                        'row': index + 2,
                        'success_count': success_count,
                        'error_count': error_count,
                        'errors': errors
                    }), 400

        # 清理临时文件
        os.remove(temp_path)
        os.rmdir(temp_dir)

        return jsonify({
            'message': '导入完成',
            'success_count': success_count,
            'error_count': error_count,
            'errors': errors
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Excel导出API
@product_bp.route('/api/product/export', methods=['GET'])
def export_products():
    """导出产品数据"""
    try:
        print("开始导出产品...")
        # 获取查询参数
        store_id = request.args.get('store_id')
        export_all = request.args.get('export_all', 'false').lower() == 'true'
        selected_ids = request.args.getlist('selected_ids[]')
        
        print(f"导出参数: store_id={store_id}, export_all={export_all}, selected_ids={selected_ids}")

        # 使用统一表查询产品数据
        query = UnifiedListProducts.query.filter_by(seller_id=store_id)
        
        # 如果不是导出全部且有选中的ID，则添加ID筛选
        if not export_all and selected_ids:
            query = query.filter(UnifiedListProducts.id.in_([int(id) for id in selected_ids]))
        
        # 执行查询
        products = query.all()
        
        if not products:
            return jsonify({'error': '没有找到任何产品'}), 404
            
        # 创建数据列表
        data = []
        for product in products:
            try:
                product_data = {
                    PRODUCT_HEADERS['operation_type']: 'update',
                    PRODUCT_HEADERS['category_id']: product.category_id,
                    PRODUCT_HEADERS['sku']: product.sku,
                    PRODUCT_HEADERS['ean']: product.ean,
                    PRODUCT_HEADERS['title_en']: product.title_en,
                    PRODUCT_HEADERS['title_cn']: product.title_cn,
                    PRODUCT_HEADERS['title_lt']: product.title_lt,
                    PRODUCT_HEADERS['title_lv']: product.title_lv,
                    PRODUCT_HEADERS['title_et']: product.title_et,
                    PRODUCT_HEADERS['title_fi']: product.title_fi,
                    PRODUCT_HEADERS['description_en']: product.description_en,
                    PRODUCT_HEADERS['description_lt']: product.description_lt,
                    PRODUCT_HEADERS['description_lv']: product.description_lv,
                    PRODUCT_HEADERS['description_et']: product.description_et,
                    PRODUCT_HEADERS['description_fi']: product.description_fi,
                    PRODUCT_HEADERS['image_url1']: product.image_url1,
                    PRODUCT_HEADERS['image_url2']: product.image_url2,
                    PRODUCT_HEADERS['image_url3']: product.image_url3,
                    PRODUCT_HEADERS['image_url4']: product.image_url4,
                    PRODUCT_HEADERS['image_url5']: product.image_url5,
                    PRODUCT_HEADERS['package_length']: product.package_length,
                    PRODUCT_HEADERS['package_width']: product.package_width,
                    PRODUCT_HEADERS['package_height']: product.package_height,
                    PRODUCT_HEADERS['package_weight']: product.package_weight,
                    PRODUCT_HEADERS['notes']: product.notes
                }
                data.append(product_data)
            except Exception as e:
                continue

        if not data:
            return jsonify({'error': '没有有效的数据可以导出'}), 404

        # 创建DataFrame并导出到Excel
        df = pd.DataFrame(data)
        
        # 保存到内存中
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='产品列表')
            
            # 获取工作表
            worksheet = writer.sheets['产品列表']
            
            print("设置Excel样式...")
            # 设置表头样式
            for cell in worksheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
            
            # 设置列宽
            for column in worksheet.columns:
                worksheet.column_dimensions[column[0].column_letter].width = 14

        excel_file.seek(0)
        print("准备发送文件...")

        return send_file(
            excel_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'店铺产品导出_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )

    except Exception as e:
        print(f"导出过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/api/product/add', methods=['POST'])
def add_product():
    try:
        data = request.get_json()
        
        # 网页添加产品验证必填字段
        required_fields = [
            'seller_id', 'sku', 'category_id', 'title_en',
            'description_en', 'package_length', 'package_width',
            'package_height', 'package_weight'
        ]
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填字段: {field}'}), 400
        
        # 处理所有字段的nan值和空值
        for key in data:
            if isinstance(data[key], str):
                # 检查是否为'nan'字符串或空字符串
                if data[key].lower() == 'nan' or not data[key].strip():
                    data[key] = ''
            elif pd.isna(data[key]):  # 检查是否为pandas的NaN值
                data[key] = ''
        
        # 获取店铺对应的产品表
        store = Store.query.filter_by(seller_id=data['seller_id']).first()
        if not store:
            return jsonify({'error': '店铺不存在'}), 404
            
        ProductModel = store.get_list_products_model()
        if not ProductModel:
            return jsonify({'error': '无法获取产品表'}), 500
            
        # 检查SKU是否已存在
        existing_product = ProductModel.query.filter_by(
            seller_id=data['seller_id'],
            sku=data['sku']
        ).first()
        
        if existing_product:
            return jsonify({'error': 'SKU已存在'}), 400
            
        # 创建新产品
        new_product = ProductModel(
            seller_id=data['seller_id'],
            sku=data['sku'],
            ean=data.get('ean', ''),
            category_id=data['category_id'],
            title_en=data['title_en'],
            title_cn=data['title_cn'],
            title_lt=data.get('title_lt', ''),
            title_lv=data.get('title_lv', ''),
            title_et=data.get('title_et', ''),
            title_fi=data.get('title_fi', ''),
            description_en=data['description_en'],
            description_lt=data.get('description_lt', ''),
            description_lv=data.get('description_lv', ''),
            description_et=data.get('description_et', ''),
            description_fi=data.get('description_fi', ''),
            image_url1=data.get('image_url1', ''),
            image_url2=data.get('image_url2', ''),
            image_url3=data.get('image_url3', ''),
            image_url4=data.get('image_url4', ''),
            image_url5=data.get('image_url5', ''),
            package_length=float(data['package_length']),
            package_width=float(data['package_width']),
            package_height=float(data['package_height']),
            package_weight=float(data['package_weight']),
            notes=data.get('notes', '')
        )
        
        try:
            db.session.add(new_product)
            db.session.commit()
            return jsonify({'message': '产品添加成功', 'id': new_product.id})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'保存失败: {str(e)}'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500 

# 修改批量翻译API路由
@product_bp.route('/api/product/batch-translate', methods=['POST'])
async def batch_translate_products():
    try:
        data = request.get_json()
        products = data.get('products', [])
        
        if not products:
            return jsonify({"error": "没有要翻译的产品"}), 400

        # 使用翻译工厂获取正确的翻译器实例
        translator = TranslatorFactory.get_translator()
        
        # 准备翻译数据
        translation_data = []
        for product in products:
            # 获取产品信息
            product_info = UnifiedListProducts.query.filter_by(sku=product['sku']).first()
            if not product_info:
                continue
            
            translation_data.append({
                'sku': product['sku'],
                'title': product_info.title_en,
                'description': product_info.description_en
            })

        if not translation_data:
            return jsonify({
                "success": False,
                "error": "没有找到有效的产品数据"
            }), 400

        # 执行批量翻译
        if isinstance(translator, DeepSeekhuoshan):
            try:
                # 使用新的批量翻译函数
                results = await translator.translate_batch_products_async(translation_data)
                
                # 更新数据库
                success_count = 0
                failed_count = 0
                errors = []
                
                for i, result in enumerate(results):
                    try:
                        product_info = UnifiedListProducts.query.filter_by(sku=translation_data[i]['sku']).first()
                        if not product_info:
                            continue

                        if result.get('error'):
                            failed_count += 1
                            errors.append(f"SKU {translation_data[i]['sku']}: {result['error']}")
                            continue

                        # 更新翻译结果
                        if result['title']:
                            product_info.title_lt = result['title'].get('LT', product_info.title_lt)
                            product_info.title_lv = result['title'].get('LV', product_info.title_lv)
                            product_info.title_et = result['title'].get('ET', product_info.title_et)
                            product_info.title_fi = result['title'].get('FI', product_info.title_fi)

                        if result['description']:
                            product_info.description_lt = result['description'].get('LT', product_info.description_lt)
                            product_info.description_lv = result['description'].get('LV', product_info.description_lv)
                            product_info.description_et = result['description'].get('ET', product_info.description_et)
                            product_info.description_fi = result['description'].get('FI', product_info.description_fi)

                        success_count += 1

                    except Exception as e:
                        failed_count += 1
                        errors.append(f"SKU {translation_data[i]['sku']}: {str(e)}")

                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    return jsonify({
                        "success": False,
                        "error": f"保存翻译结果时出错: {str(e)}"
                    }), 500

                return jsonify({
                    "success": True,
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "errors": errors
                })

            except Exception as e:
                logger.error(f"Batch translation error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        elif isinstance(translator, MTranServer):
            try:
                # 使用MT翻译器的批量翻译函数
                results = await translator.translate_batch_products_async(translation_data)
                
                # 更新数据库
                success_count = 0
                failed_count = 0
                errors = []
                
                for result in results:
                    try:
                        product_info = UnifiedListProducts.query.filter_by(sku=result['sku']).first()
                        if not product_info:
                            continue

                        if result.get('error'):
                            failed_count += 1
                            errors.append(f"SKU {result['sku']}: {result['error']}")
                            continue

                        # 更新翻译结果
                        if result['title']:
                            product_info.title_lt = result['title'].get('LT', product_info.title_lt)
                            product_info.title_lv = result['title'].get('LV', product_info.title_lv)
                            product_info.title_et = result['title'].get('ET', product_info.title_et)
                            product_info.title_fi = result['title'].get('FI', product_info.title_fi)

                        if result['description']:
                            product_info.description_lt = result['description'].get('LT', product_info.description_lt)
                            product_info.description_lv = result['description'].get('LV', product_info.description_lv)
                            product_info.description_et = result['description'].get('ET', product_info.description_et)
                            product_info.description_fi = result['description'].get('FI', product_info.description_fi)

                        success_count += 1

                    except Exception as e:
                        failed_count += 1
                        error_msg = f"SKU {result['sku']}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)

                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    return jsonify({
                        "success": False,
                        "error": f"保存翻译结果时出错: {str(e)}"
                    }), 500

                return jsonify({
                    "success": True,
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "errors": errors
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
            
        #组合翻译情况下
        elif isinstance(translator, DeepSeekMTranServer):
            try:
                # 使用组合翻译器的批量翻译函数
                results = await translator.translate_batch_products_async(translation_data)
                
                # 更新数据库
                success_count = 0
                failed_count = 0
                errors = []
                
                for result in results:
                    try:
                        product_info = UnifiedListProducts.query.filter_by(sku=result['sku']).first()
                        if not product_info:
                            continue

                        if result.get('error'):
                            failed_count += 1
                            errors.append(f"SKU {result['sku']}: {result['error']}")
                            continue

                        # 更新翻译结果 - 标题来自火山引擎，描述来自MT翻译
                        if result['title']:
                            product_info.title_lt = result['title'].get('LT', product_info.title_lt)
                            product_info.title_lv = result['title'].get('LV', product_info.title_lv)
                            product_info.title_et = result['title'].get('ET', product_info.title_et)
                            product_info.title_fi = result['title'].get('FI', product_info.title_fi)

                        if result['description']:
                            product_info.description_lt = result['description'].get('LT', product_info.description_lt)
                            product_info.description_lv = result['description'].get('LV', product_info.description_lv)
                            product_info.description_et = result['description'].get('ET', product_info.description_et)
                            product_info.description_fi = result['description'].get('FI', product_info.description_fi)

                        success_count += 1
                        logger.debug(f"产品 {result['sku']} 翻译成功 (组合模式)")

                    except Exception as e:
                        failed_count += 1
                        error_msg = f"SKU {result['sku']}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)

                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    return jsonify({
                        "success": False,
                        "error": f"保存翻译结果时出错: {str(e)}"
                    }), 500

                return jsonify({
                    "success": True,
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "errors": errors
                })

            except Exception as e:
                logger.error(f"组合模式批量翻译错误: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500

        else:
            # 其他翻译器保持原有逻辑
            results = []
            success_count = 0
            failed_count = 0
            errors = []
            
            for product in translation_data:
                try:
                    # 获取产品信息
                    product_info = UnifiedListProducts.query.filter_by(sku=product['sku']).first()
                    if not product_info:
                        failed_count += 1
                        errors.append(f"SKU {product['sku']}: 产品不存在")
                        continue

                    # 异步翻译标题和描述
                    translations = await translator.translate_product_full_async(
                        product_info.title_en,
                        product_info.description_en,
                        "EN"
                    )

                    # 更新产品信息
                    if translations['title']:
                        product_info.title_lt = translations['title'].get('LT', product_info.title_lt)
                        product_info.title_lv = translations['title'].get('LV', product_info.title_lv)
                        product_info.title_et = translations['title'].get('ET', product_info.title_et)
                        product_info.title_fi = translations['title'].get('FI', product_info.title_fi)

                    if translations['description']:
                        product_info.description_lt = translations['description'].get('LT', product_info.description_lt)
                        product_info.description_lv = translations['description'].get('LV', product_info.description_lv)
                        product_info.description_et = translations['description'].get('ET', product_info.description_et)
                        product_info.description_fi = translations['description'].get('FI', product_info.description_fi)

                    db.session.commit()
                    success_count += 1
                    results.append({
                        'sku': product['sku'],
                        'success': True
                    })

                except Exception as e:
                    db.session.rollback()
                    failed_count += 1
                    error_msg = f"SKU {product['sku']}: {str(e)}"
                    errors.append(error_msg)
                    results.append({
                        'sku': product['sku'],
                        'success': False,
                        'error': str(e)
                    })

            return jsonify({
                'success': True,
                'success_count': success_count,
                'failed_count': failed_count,
                'errors': errors,
                'results': results
            })

    except Exception as e:
        logger.error(f"Batch translation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    
# 修改单个产品内翻译API路由
@product_bp.route('/api/product/translate', methods=['POST'])
def translate_product():
    try:
        data = request.get_json()
        text = data.get('text')
        source_lang = data.get('source_lang', 'AUTO')
        target_langs = data.get('target_langs', [])
        content_type = data.get('content_type')
        
        if not text or not target_langs:
            return jsonify({"error": "缺少必要参数"}), 400
            
        translator = TranslatorFactory.get_translator()
        translations = {}
        has_error = False
        error_message = None

        # 检查是否是可以一次性返回所有语言的翻译器（火山引擎或组合翻译）
        if (isinstance(translator, DeepSeekhuoshan) or 
            (isinstance(translator, DeepSeekMTranServer) and content_type.lower() == 'title')):
                        
            # 对于支持一次性返回所有语言的翻译器，使用"all"作为目标语言
            result = translator.translate(
                text=text,
                source_lang=source_lang,
                target_lang="all",
                content_type=content_type
            )
            
            if result['success']:
                # 直接使用返回的翻译结果，提取所需的目标语言
                if isinstance(result['text'], dict):
                    # 如果返回的是字典格式（包含所有语言）
                    translations = {lang.lower(): result['text'].get(lang.upper()) for lang in target_langs}
                else:
                    # 如果返回的是单一文本
                    # logger.info(f"翻译器返回了单一文本而非多语言字典: {result['text']}")
                    translations = {target_langs[0].lower(): result['text']}
            else:
                has_error = True
                error_message = result.get('error')
        else:
            # 其他翻译器保持原有逻辑
            for target_lang in target_langs:
                try:
                    result = translator.translate(
                        text=text,
                        source_lang=source_lang,
                        target_lang=target_lang.upper(),
                        content_type=content_type
                    )
                    if result['success']:
                        translations[target_lang] = result['text']
                    else:
                        translations[target_lang] = None
                        has_error = True
                        error_message = result.get('error')
                except Exception as e:
                    translations[target_lang] = None
                    has_error = True
                    error_message = str(e)

        return jsonify({
            "success": not has_error,
            "translations": translations,
            "error": error_message if has_error else None
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@product_bp.route('/api/product/batch-modify', methods=['POST'])
def batch_modify_products():
    """批量修改产品信息"""
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        category_id = data.get('category_id')
        sku_change = data.get('sku_change')
        
        if not product_ids:
            return jsonify({'error': '没有选择要修改的产品'}), 400
            
        # 获取要修改的产品
        products = UnifiedListProducts.query.filter(
            UnifiedListProducts.id.in_(product_ids)
        ).all()
        
        if not products:
            return jsonify({'error': '未找到要修改的产品'}), 404
            
        updated_count = 0
        
        for product in products:
            try:
                modified = False
                
                # 修改分类ID
                if category_id is not None:
                    product.category_id = int(category_id)
                    modified = True
                
                # 修改SKU前缀
                if sku_change:
                    old_prefix = sku_change.get('old_prefix')
                    new_prefix = sku_change.get('new_prefix')
                    if old_prefix and new_prefix and product.sku.startswith(old_prefix):
                        # 替换SKU前缀
                        product.sku = new_prefix + product.sku[len(old_prefix):]
                        modified = True
                
                if modified:
                    updated_count += 1
                    
            except Exception as e:
                logger.error(f"修改产品 {product.id} 时出错: {str(e)}")
                continue
        
        if updated_count > 0:
            try:
                db.session.commit()
                return jsonify({
                    'message': f'成功修改 {updated_count} 个产品',
                    'updated_count': updated_count
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'error': f'保存更改时出错: {str(e)}'}), 500
        else:
            return jsonify({'message': '没有产品被修改', 'updated_count': 0})
            
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
    
async def check_single_barcode(session, ean, token, products_dict=None):
    """异步查询单个EAN码信息
    Args:
        session: aiohttp会话
        ean: EAN码
        token: 认证token
        products_dict: EAN到产品的映射字典，用于优化数据库查询
    """
    try:
        url = "https://pmpapi.pigugroup.eu/v3/products/product-modifications/barcodes"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Pigu-mp {token}"
        }
        
        params = {
            "ean": ean
        }
        
        # 获取代理设置
        PROXY_HOST = SystemSettings.get_setting('proxy_host')
        PROXY_PORT = SystemSettings.get_setting('proxy_port')
        PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"
        
        try:
            async with session.get(url, headers=headers, params=params, proxy=PROXY_URL) as response:
                # 如果状态码是200，更新产品状态为xml_success
                if response.status == 200 and products_dict is not None:
                    # 从字典中获取产品，避免单独的数据库查询
                    product = products_dict.get(ean)
                    if product:
                        product.status = 'xml_success'
                
                return {
                    'status_code': response.status,
                    'ean': ean
                }
                    
        except Exception as e:
            return {
                'status_code': 0,  # 使用0表示请求异常
                'ean': ean
            }
            
    except Exception as e:
        return {
            'status_code': 0,  # 使用0表示请求异常
            'ean': ean
        }

@product_bp.route('/api/product/batch-inquire', methods=['POST'])
async def batch_inquire_products():
    """批量查询EAN上架情况"""
    try:
        data = request.get_json()
        seller_id = data.get('seller_id')
        eans = data.get('eans', [])
        
        if not seller_id or not eans:
            return jsonify({"error": "缺少必要参数"}), 400

        # 获取店铺信息
        store = Store.query.filter_by(seller_id=seller_id).first()
        if not store:
            return jsonify({"error": "未找到店铺信息"}), 400
            
        # 获取token
        token = store.get_valid_token()
        if not token:
            return jsonify({"error": "获取token失败"}), 400

        # 一次性查询所有产品并创建映射
        products = UnifiedListProducts.query.filter(
            UnifiedListProducts.seller_id == seller_id,
            UnifiedListProducts.ean.in_(eans)
        ).all()
        products_dict = {p.ean: p for p in products}

        # 创建异步会话
        async with aiohttp.ClientSession() as session:
            # 并发执行所有查询任务
            tasks = [check_single_barcode(session, ean, token, products_dict) for ean in eans]
            results = await asyncio.gather(*tasks)

            # 统计成功数量
            success_count = sum(1 for result in results if result['status_code'] == 200)

            # 一次性提交所有更改
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                logger.error(f"保存状态更新时出错: {str(e)}")

            return jsonify({
                "success": True,
                "success_count": success_count
            })

    except Exception as e:
        logger.error(f"批量查询EAN上架状态时出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


